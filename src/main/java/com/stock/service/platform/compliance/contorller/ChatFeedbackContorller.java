package com.stock.service.platform.compliance.contorller;

import com.stock.core.dto.JsonResponse;
import com.stock.core.web.DownloadView;
import com.stock.service.platform.compliance.dto.ChatFeedbackQueryDto;
import com.stock.service.platform.compliance.service.ChatFeedbackService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/feedbackReply")
public class ChatFeedbackContorller {

    @Resource
    private ChatFeedbackService chatFeedbackService;

    /**
     * 查询表格
     */
    @PostMapping("getTableList")
    public JsonResponse <Map<String, Object>> getTableList(@RequestBody ChatFeedbackQueryDto chatFeedbackQueryDto) {
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(chatFeedbackService.getTableList(chatFeedbackQueryDto));
        return  response;
    }

    /**
     * 获取下拉
     */
    @RequestMapping(value = "getDropDown")
    @ResponseBody
    public JsonResponse<Map<String, Object>> getDropDown(){
        JsonResponse<Map<String, Object>> response = new JsonResponse<>();
        response.setResult(chatFeedbackService.getDropDown());
        return response;
    }

    /**
     * 导出列表
     */
    @RequestMapping(value = "exportFieldList")
    @ResponseBody
    public ModelAndView exportFieldList(@RequestBody ChatFeedbackQueryDto chatFeedbackQueryDto, HttpServletResponse response) throws IOException {
        Map<String,Object> resultMap = chatFeedbackService.exportFieldList(chatFeedbackQueryDto);
        ModelAndView mv = new ModelAndView();
        mv.setView(new DownloadView());
        mv.addObject(DownloadView.EXPORT_FILE, resultMap.get("inputStream"));
        mv.addObject(DownloadView.EXPORT_FILE_NAME,  resultMap.get("fileName"));
        mv.addObject(DownloadView.EXPORT_FILE_TYPE, DownloadView.FILE_TYPE.XLSX);
        response.setHeader("fileName", java.net.URLEncoder.encode(resultMap.get("fileName").toString(), "utf-8"));
        return mv;
    }

}
